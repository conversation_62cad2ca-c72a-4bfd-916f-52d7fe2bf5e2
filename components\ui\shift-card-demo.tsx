"use client"

import { motion } from "motion/react"
import { ShiftCard } from "@/components/ui/shift-card"
import { TextureButton } from "@/components/ui/texture-button"
import Image from "next/image"

interface ShiftCardDemoProps {
  product: {
    ProductCard: {
      productCategory: string
      productName: string
      productLogo: {
        url: string
      }
      productRedirectURL: string | null
      id: string
      productDescription: string
      label: string | null
    }
  }
  onViewProduct: () => void
}

export function ShiftCardDemo({ product, onViewProduct }: ShiftCardDemoProps) {
  const { ProductCard } = product

  // Content for the top part of the card (no logo when not hovering)
  const topContent = (
    <div className="bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-md text-gray-800 dark:text-gray-200 shadow-[0px_1px_1px_0px_rgba(0,0,0,0.05),0px_1px_1px_0px_rgba(255,252,240,0.5)_inset,0px_0px_0px_1px_hsla(0,0%,100%,0.1)_inset,0px_0px_1px_0px_rgba(28,27,26,0.5)] dark:shadow-[0_1px_0_0_rgba(255,255,255,0.03)_inset,0_0_0_1px_rgba(255,255,255,0.03)_inset,0_0_0_1px_rgba(0,0,0,0.1),0_2px_2px_0_rgba(0,0,0,0.1),0_4px_4px_0_rgba(0,0,0,0.1),0_8px_8px_0_rgba(0,0,0,0.1)] backdrop-blur-sm transition-all duration-300 ease-out">
      <h3 className="text-lg p-4 font-medium transition-colors duration-300">
        <span>{ProductCard.productName}</span>
      </h3>
    </div>
  )

  // Content that animates into top from the middle (logo moves into the grey header perfectly)
  const topAnimateContent = (
    <>
      <motion.div
        transition={{ duration: 0.3, ease: "circIn" }}
        layoutId={`img-${ProductCard.id}`}
        className="absolute top-4 right-4 w-8 h-8 rounded-sm overflow-hidden bg-white/90 dark:bg-gray-900/90 p-1 shadow-sm backdrop-blur-sm border border-white/20 dark:border-gray-700/50"
      >
        {ProductCard.productLogo && (
          <Image
            src={ProductCard.productLogo.url}
            alt={ProductCard.productName}
            width={24}
            height={24}
            className="object-contain w-full h-full filter-none transition-transform duration-300"
          />
        )}
      </motion.div>
    </>
  )

  // Content that shows in the middle when not hovered (factory behavior)
  const middleContent = (
    <motion.div
      layoutId={`img-${ProductCard.id}`}
      className="w-[150px] h-[150px] rounded-lg overflow-hidden"
    >
      {ProductCard.productLogo && (
        <Image
          src={ProductCard.productLogo.url}
          alt={ProductCard.productName}
          width={150}
          height={150}
          className="object-contain w-full h-full filter-none"
        />
      )}
    </motion.div>
  )

  // Content for the bottom part of the card that shows more details on hover
  const bottomContent = (
    <div className="flex w-full flex-col gap-3 bg-gradient-to-br from-gray-900/95 to-black/95 dark:from-gray-900/95 dark:to-black/95 border-t border-t-gray-700/50 dark:border-t-gray-700/50 rounded-t-lg px-4 py-3 backdrop-blur-md">
      <div className="font-sans text-[14px] font-medium text-gray-200 dark:text-gray-200 flex gap-2 items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          width="1em"
          height="1em"
          className="text-gray-300 dark:text-gray-300"
        >
          <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
          <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
        </svg>
        <p>Discover & Explore</p>
      </div>

      <div className="w-full text-pretty font-sans text-[13px] leading-5 text-gray-300 dark:text-gray-300">
        {ProductCard.productDescription || 'Explore this amazing product and discover its powerful features and capabilities.'}
      </div>

      <motion.button
        onClick={onViewProduct}
        className="w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 shadow-sm hover:shadow-md border border-red-500/30 hover:border-red-400/50 relative overflow-hidden"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        {/* Shimmer effect */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12"
          initial={{ x: "-100%" }}
          whileHover={{ x: "100%" }}
          transition={{ duration: 0.6, ease: "easeInOut" }}
        />
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          width="16"
          height="16"
          className="relative z-10"
        >
          <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
          <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
        </svg>
        <span className="relative z-10">View Product</span>
      </motion.button>
    </div>
  )

  return (
    <div className="flex justify-center items-center">
      <ShiftCard
        className="bg-transparent"
        topContent={topContent}
        topAnimateContent={topAnimateContent}
        middleContent={middleContent}
        bottomContent={bottomContent}
      />
    </div>
  )
}